{"name": "http-signature", "description": "Reference implementation of Joyent's HTTP Signature scheme.", "version": "1.2.0", "license": "MIT", "author": "Joyent, Inc", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "git://github.com/joyent/node-http-signature.git"}, "homepage": "https://github.com/joyent/node-http-signature/", "bugs": "https://github.com/joyent/node-http-signature/issues", "keywords": ["https", "request"], "engines": {"node": ">=0.8", "npm": ">=1.3.7"}, "main": "lib/index.js", "scripts": {"test": "tap test/*.js"}, "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "devDependencies": {"tap": "0.4.2", "uuid": "^2.0.2"}}