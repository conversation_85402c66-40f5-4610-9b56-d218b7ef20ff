const { By, until } = require("selenium-webdriver");
const request = require('request');

/**
 * hCaptcha Solver using SolveCaptcha API (coordinates method)
 *
 * Sends full challenge image (base64) + instruction text to API
 * Polls for coordinate clicks and applies them inside the challenge iframe
 */

class HCaptchaSolver {
  constructor(driver, apiKey) {
    if (!driver) {
      throw new Error('Selenium WebDriver instance is required');
    }
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('Valid SolveCaptcha API key is required');
    }

    this.driver = driver;
    this.apiKey = apiKey;
    this.maxRetries = 3;
    this.waitTimeout = 30000;
    this.pollInterval = 2000;
    this.maxPollTime = 180000;
    this.maxRoundsPerChallenge = 2;
    this.challengeContainerSelector = null;

    // SolveCaptcha API endpoints
    this.submitUrl = 'https://api.solvecaptcha.com/in.php';
    this.resultUrl = 'https://api.solvecaptcha.com/res.php';
  }

  /**
   * Get current API balance
   * @returns {Promise<number|null>} Balance amount or null if error
   */
  async getBalance() {
    try {
      const balanceUrl = `${this.resultUrl}?key=${this.apiKey}&action=getbalance&json=1`;
      const response = await this.makeApiRequest(balanceUrl);
      if (response.status === 1) {
        return parseFloat(response.request) || 0;
      }
      return null;
    } catch (e) {
      console.log('Error getting balance:', e.message);
      return null;
    }
  }

  async makeApiRequest(url, formData = null, method = 'GET', customHeaders = {}) {
    return new Promise((resolve, reject) => {
      if (method === 'GET') {
        request({
          method: 'GET',
          url,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...customHeaders
          }
        }, (error, response) => {
          if (error) return reject(error);
          try {
            const jsonResponse = JSON.parse(response.body);
            resolve(jsonResponse);
          } catch (e) {
            resolve({ status: 0, error: 'Invalid JSON response', raw: response.body ? response.body.toString().trim() : 'No response body' });
          }
        });
      } else {
        const options = {
          method: 'POST',
          url,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...customHeaders
          }
        };

        if (customHeaders['Content-Type'] === 'application/json') {
          options.json = formData;
        } else {
          options.form = formData; // use x-www-form-urlencoded for captcha providers
        }

        request(options, (error, response) => {
          if (error) return reject(error);
          try {
            const jsonResponse = JSON.parse(response.body);
            resolve(jsonResponse);
          } catch (e) {
            resolve({ status: 0, error: 'Invalid JSON response', raw: response.body ? response.body.toString().trim() : 'No response body' });
          }
        });
      }
    });
  }

  async detectHCaptcha() {
    try {
      await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"]'));
      return true;
    } catch {
      try {
        await this.driver.findElement(By.css('.h-captcha, [data-hcaptcha-response], [data-sitekey]'));
        return true;
      } catch {
        return false;
      }
    }
  }

  async extractSitekey() {
    console.log('Attempting to extract sitekey from page...');
    
    try {
      const sitekeyElements = await this.driver.findElements(By.css('[data-sitekey]'));
      for (const element of sitekeyElements) {
        const sitekey = await element.getAttribute('data-sitekey');
        if (sitekey && sitekey.length > 10) {
          console.log('✓ Found sitekey via data-sitekey:', sitekey);
          return sitekey;
        }
      }
    } catch {}

    try {
      const pageSource = await this.driver.getPageSource();
      const patterns = [
        /data-sitekey=["']([a-f0-9-]{8,})['"]/gi,
        /["']sitekey["']:\s*["']([a-f0-9-]{8,})["']/gi,
        /sitekey:\s*["']([a-f0-9-]{8,})["']/gi,
        /hcaptcha.*?sitekey.*?["']([a-f0-9-]{8,})["']/gi,
        /["']([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})["']/gi
      ];
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(pageSource)) !== null) {
          const sitekey = match[1];
          if (sitekey && sitekey.length >= 8) {
            console.log('✓ Found sitekey in page source with pattern:', pattern.source);
            console.log('✓ Sitekey:', sitekey);
            return sitekey;
          }
        }
      }
    } catch {}

    console.log('❌ Sitekey extraction failed');
    throw new Error('Could not extract sitekey from page');
  }

  async testApiKey() {
    console.log('Testing SolveCaptcha API key...');
    try {
      const balanceUrl = `${this.resultUrl}?key=${this.apiKey}&action=getbalance&json=1`;
      const response = await this.makeApiRequest(balanceUrl);
      if (response.status === 1) {
        console.log('✓ API key is valid. Balance:', response.request);
        return true;
      }
      if (response.raw === 'ERROR_WRONG_USER_KEY') {
        console.log('❌ API key is invalid');
        return false;
      }
      console.log('⚠️ API key test response:', response);
      return true;
    } catch (e) {
      console.log('Error testing API key:', e.message);
      return false;
    }
  }

  async ensureChallengeOpen() {
    // Try to find challenge iframe; if not open, click checkbox to open it
    let challengeIframe;
    try {
      challengeIframe = await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
      return challengeIframe;
    } catch {}

    // Try to click checkbox iframe
    try {
      const checkboxIframe = await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="checkbox"]'));
      await this.driver.switchTo().frame(checkboxIframe);
      try {
        const checkbox = await this.driver.findElement(By.css('div, iframe, body'));
        await checkbox.click();
      } catch {}
      await this.driver.switchTo().defaultContent();
      await this.driver.sleep(1500);
    } catch {}

    // Wait for challenge iframe to appear
    try {
      challengeIframe = await this.driver.wait(
        until.elementLocated(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]')),
        10000
      );
      return challengeIframe;
    } catch (e) {
      console.log('⚠️ Challenge iframe not found:', e.message);
      return null;
    }
  }

  async getChallengeImageAndInstruction() {
    console.log('🔍 Capturing challenge image and instruction...');

    const challengeIframe = await this.ensureChallengeOpen();
    if (!challengeIframe) {
      console.log('❌ Cannot capture challenge: iframe not found');
      return null;
    }

    await this.driver.switchTo().frame(challengeIframe);

    // Try to read instruction text from common selectors
    let instruction = '';
    const instructionSelectors = [
      '.prompt-text',
      '.task-instructions',
      '.challenge-content .prompt-text',
      '.challenge-view .prompt-text',
      'div:contains("Please click")'
    ];
    for (const sel of instructionSelectors) {
      try {
        const el = await this.driver.findElement(By.css(sel));
        const text = await el.getText();
        if (text && text.length > 3) {
          instruction = text;
                break;
              }
      } catch {}
    }
    if (!instruction) {
      try {
        instruction = await this.driver.executeScript('return document.body.innerText || "";');
        const m = instruction.match(/Please\s+click[^\n]+/i);
        instruction = m ? m[0] : 'Click on all images matching the goal';
      } catch {
        instruction = 'Click on all images matching the goal';
      }
    }

    // Find the challenge image/grid container and screenshot it
    const containerSelectors = [
      'div[class*="task-grid"]',
      'div[class*="image-grid"]',
      'div[class*="challenge"]',
      'div[role="dialog"]',
      'body'
    ];

    let containerEl = null;
    for (const sel of containerSelectors) {
      try {
        const el = await this.driver.findElement(By.css(sel));
        if (el) {
          containerEl = el;
              break;
            }
      } catch {}
    }

    if (!containerEl) {
      console.log('⚠️ Could not find a specific container, using body');
      containerEl = await this.driver.findElement(By.css('body'));
      this.challengeContainerSelector = 'body';
    } else {
      // Record selector used for later coordinate mapping
      try {
        // Pick the first matching selector that found this element
        for (const sel of containerSelectors) {
          try {
            const test = await this.driver.findElement(By.css(sel));
            if (test) { this.challengeContainerSelector = sel; break; }
          } catch {}
        }
      } catch {}
    }

    let base64Image = '';
    try {
      base64Image = await containerEl.takeScreenshot(true);
    } catch (e) {
      console.log('❌ Element screenshot failed, fallback to full frame screenshot:', e.message);
      try {
        base64Image = await this.driver.takeScreenshot();
      } catch (e2) {
        console.log('❌ Full screenshot failed:', e2.message);
      }
    }

    // Back to main content
    await this.driver.switchTo().defaultContent();

    if (!base64Image) {
      console.log('❌ No screenshot captured');
      return null;
    }

    console.log('✅ Captured challenge screenshot and instruction');
    return { imageBase64: base64Image, instruction };
  }

  async submitCaptcha(sitekey, pageUrl) {
    console.log('Submitting captcha to SolveCaptcha API using coordinates method...');

    // Capture challenge data required by the service
    const challengeData = await this.getChallengeImageAndInstruction();
    if (!challengeData) {
      console.log('❌ Missing challenge data (image/instruction). Cannot submit coordinates captcha.');
      return null;
    }

    const formData = {
      key: this.apiKey,
      method: 'base64',
      sitekey: sitekey,
      pageurl: pageUrl,
      json: 1,
      coordinatescaptcha: 1,
      textinstructions: challengeData.instruction,
      body: challengeData.imageBase64,
      domain: 'hcaptcha.com'
    };

    console.log('📤 Submitting with coordinates method:', {
      key: this.apiKey.substring(0, 8) + '...',
      method: formData.method,
      sitekey: formData.sitekey,
      pageurl: formData.pageurl,
      json: formData.json,
      coordinatescaptcha: formData.coordinatescaptcha,
      textinstructions: formData.textinstructions.substring(0, 80) + '...'
    });

    try {
      const response = await this.makeApiRequest(this.submitUrl, formData, 'POST');
      if (response.status === 1) {
        console.log('✓ Captcha submitted successfully. Task ID:', response.request);
        return response.request;
      }
      console.log('❌ Failed to submit captcha:', response);
        return null;
    } catch (e) {
      console.log('Error submitting captcha:', e.message);
      return null;
    }
  }

  async pollForSolution(taskId) {
    console.log('Polling for captcha solution...');
    const startTime = Date.now();
    let pollCount = 0;
    
    while (Date.now() - startTime < this.maxPollTime) {
      pollCount++;
      try {
        const url = `${this.resultUrl}?key=${this.apiKey}&action=get&id=${taskId}&json=1`;
        const response = await this.makeApiRequest(url);
        if (response.status === 1) {
          console.log('🎉 Captcha solved successfully! Coordinates received');
          return { coordinates: response.request, userAgent: response.useragent || null };
        }
        if (response.request === 'CAPCHA_NOT_READY' || response.request === 'NOT_READY' || response.request === 'CAPTCHA_NOT_READY') {
          await this.driver.sleep(this.pollInterval);
          continue;
        }
        console.log('❌ Error / unexpected response:', response);
        await this.driver.sleep(this.pollInterval);
      } catch (e) {
        console.log('🚨 Error polling for solution:', e.message);
        await this.driver.sleep(this.pollInterval);
      }
    }

    console.log('⏰ Timeout waiting for captcha solution');
    return null;
  }

  async executeCoordinates(coordinatesPayload) {
    console.log('🔧 Executing coordinates-based captcha solving...');

    // Debug: Log the raw payload
    console.log('📊 Raw coordinates payload:', JSON.stringify(coordinatesPayload, null, 2));

    // Normalize incoming coordinates/actions: accept JSON string, array, or structured object
    let coordsData = null;
    try {
      if (typeof coordinatesPayload === 'string') {
        const str = coordinatesPayload.trim();
        if (str.startsWith('{') || str.startsWith('[')) {
          coordsData = JSON.parse(str);
        }
      } else if (Array.isArray(coordinatesPayload)) {
        coordsData = coordinatesPayload;
      } else if (coordinatesPayload && typeof coordinatesPayload === 'object') {
        coordsData = coordinatesPayload.coordinates || coordinatesPayload.points || coordinatesPayload.answer || coordinatesPayload.clicks || coordinatesPayload;
      }
    } catch (e) {
      console.log('❌ Error parsing coordinates:', e.message);
    }

    if (!coordsData) {
      console.log('❌ Invalid/unknown coordinates payload format');
      console.log('📊 Payload type:', typeof coordinatesPayload);
      console.log('📊 Payload value:', coordinatesPayload);
      return false;
    }

    console.log('✅ Parsed coordinates data:', JSON.stringify(coordsData, null, 2));

    // Ensure challenge is open and switch into iframe
    const challengeIframe = await this.ensureChallengeOpen();
    if (!challengeIframe) return false;
    await this.driver.switchTo().frame(challengeIframe);

    // Find the same container area to map coordinates against
    const containerSelectors = [
      'div[class*="task-grid"]',
      'div[class*="image-grid"]',
      'div[class*="challenge"]',
      'div[role="dialog"]',
      'body'
    ];

    let containerSelUsed = this.challengeContainerSelector || 'body';
    if (!this.challengeContainerSelector) {
      for (const sel of containerSelectors) {
        try {
          const el = await this.driver.findElement(By.css(sel));
          if (el) { containerSelUsed = sel; break; }
        } catch {}
      }
    }

    // Perform clicks/drags relative to the container, then try Verify
    try {
      await this.driver.executeScript(
        function(sel, actions) {
          const container = document.querySelector(sel) || document.body;
          const rect = container.getBoundingClientRect();

          // Helpers
          function toClient(p) {
            if (!p) return null;
            let x = (typeof p.x === 'number') ? p.x : (Array.isArray(p) ? p[0] : null);
            let y = (typeof p.y === 'number') ? p.y : (Array.isArray(p) ? p[1] : null);
            if (x == null || y == null) return null;
            if (x >= 0 && x <= 1 && y >= 0 && y <= 1) {
              x = x * rect.width;
              y = y * rect.height;
            }
            return { cx: rect.left + x, cy: rect.top + y };
          }

          function clickAt(clientX, clientY) {
            const target = document.elementFromPoint(clientX, clientY) || container;
            const down = new MouseEvent('mousedown', { view: window, bubbles: true, cancelable: true, clientX, clientY });
            const pdown = new PointerEvent('pointerdown', { bubbles: true, cancelable: true, clientX, clientY, pointerType: 'mouse' });
            const up = new MouseEvent('mouseup', { view: window, bubbles: true, cancelable: true, clientX, clientY });
            const pup = new PointerEvent('pointerup', { bubbles: true, cancelable: true, clientX, clientY, pointerType: 'mouse' });
            const click = new MouseEvent('click', { view: window, bubbles: true, cancelable: true, clientX, clientY });
            target.dispatchEvent(pdown);
            target.dispatchEvent(down);
            target.dispatchEvent(pup);
            target.dispatchEvent(up);
            target.dispatchEvent(click);
          }

          function getGridInfo() {
            // Try to identify grid tiles for more reliable clicking
            const grid = container.querySelector('[class*="task-grid"], [class*="image-grid"], .challenge-grid, .grid') || container;
            const tiles = Array.from(grid.querySelectorAll('[role="button"], [class*="task-image"], .tile, .image, button'));
            let cols = 0, rows = 0;
            if (tiles.length > 0) {
              const approx = Math.round(Math.sqrt(tiles.length));
              cols = approx;
              rows = Math.ceil(tiles.length / cols);
            }
            const r = grid.getBoundingClientRect();
            return { grid, tiles, cols, rows, rect: r };
          }

          function clickTileAtPoint(px, py) {
            const gi = getGridInfo();
            if (!gi.tiles.length) return false;
            const width = gi.rect.width;
            const height = gi.rect.height;
            let cols = gi.cols || 3;
            let rows = gi.rows || 3;
            const cellW = width / cols;
            const cellH = height / rows;
            const col = Math.max(0, Math.min(cols - 1, Math.floor(px / cellW)));
            const row = Math.max(0, Math.min(rows - 1, Math.floor(py / cellH)));
            const idx = row * cols + col;
            const tile = gi.tiles[idx];
            if (tile) { try { tile.click(); return true; } catch(e) { /* ignore */ } }
            return false;
          }

          function drag(from, to) {
            const tgt = document.elementFromPoint(from.cx, from.cy) || container;
            const evts = [
              new MouseEvent('mousedown', { bubbles: true, cancelable: true, clientX: from.cx, clientY: from.cy }),
              new MouseEvent('mousemove', { bubbles: true, cancelable: true, clientX: (from.cx+to.cx)/2, clientY: (from.cy+to.cy)/2 }),
              new MouseEvent('mousemove', { bubbles: true, cancelable: true, clientX: to.cx, clientY: to.cy }),
              new MouseEvent('mouseup', { bubbles: true, cancelable: true, clientX: to.cx, clientY: to.cy })
            ];
            // Pointer events as well (some UIs rely on them)
            const pevts = [
              new PointerEvent('pointerdown', { bubbles: true, cancelable: true, clientX: from.cx, clientY: from.cy, pointerType: 'mouse' }),
              new PointerEvent('pointermove', { bubbles: true, cancelable: true, clientX: (from.cx+to.cx)/2, clientY: (from.cy+to.cy)/2, pointerType: 'mouse' }),
              new PointerEvent('pointermove', { bubbles: true, cancelable: true, clientX: to.cx, clientY: to.cy, pointerType: 'mouse' }),
              new PointerEvent('pointerup', { bubbles: true, cancelable: true, clientX: to.cx, clientY: to.cy, pointerType: 'mouse' })
            ];
            for (const e of evts) tgt.dispatchEvent(e);
            for (const e of pevts) tgt.dispatchEvent(e);
          }

          const pages = Array.isArray(actions?.pages) ? actions.pages : [actions];
          for (let pg = 0; pg < pages.length; pg++) {
            const page = pages[pg] || {};
            const clicks = Array.isArray(page) ? page : (page.clicks || page.coordinates || page.points || page.answer || []);
            const drags = page.drags || page.drag || [];

            // Clicks
            if (Array.isArray(clicks)) {
              for (let i = 0; i < clicks.length; i++) {
                const pt = toClient(clicks[i]);
                if (pt) {
                  // Prefer tile click if grid is present, else absolute
                  const relX = pt.cx - rect.left;
                  const relY = pt.cy - rect.top;
                  if (!clickTileAtPoint(relX, relY)) {
                    clickAt(pt.cx, pt.cy);
                  }
                }
              }
            }

            // Drags
            const dragList = Array.isArray(drags) ? drags : [drags];
            for (let d = 0; d < dragList.length; d++) {
              const it = dragList[d];
              const from = toClient(it?.from || it?.start || it?.src || it?.a);
              const to = toClient(it?.to || it?.end || it?.dst || it?.b);
              if (from && to) drag(from, to);
            }
          }

          // Click a Verify/Next button if present - try multiple approaches
          const labels = ['verify', 'next', 'submit', 'check', 'continue'];
          let buttonClicked = false;
          
          // First try specific hCaptcha verify button
          const hcaptchaButtons = Array.from(document.querySelectorAll('[data-hcaptcha-widget-id] button, .h-captcha button, [class*="hcaptcha"] button'));
          for (const btn of hcaptchaButtons) {
            const text = (btn.innerText || btn.textContent || '').trim().toLowerCase();
            if (labels.some(l => text.includes(l)) || text === '' || btn.type === 'submit') {
              try { 
                btn.click(); 
                buttonClicked = true;
                console.log('Clicked hCaptcha verify button');
                break; 
              } catch(e) {}
            }
          }
          
          // If no specific hCaptcha button found, try general buttons
          if (!buttonClicked) {
            const buttons = Array.from(document.querySelectorAll('button, [role="button"], .button-submit, .submit-button'));
            for (const btn of buttons) {
              const text = (btn.innerText || btn.textContent || '').trim().toLowerCase();
              if (labels.some(l => text.includes(l))) { 
                try { 
                  btn.click(); 
                  console.log('Clicked verify button:', text);
                  break; 
                } catch(e){} 
              }
            }
          }
        },
        containerSelUsed,
        coordsData
      );
    } catch (e) {
      console.log('❌ Error dispatching coordinate clicks:', e.message);
      try { await this.driver.switchTo().defaultContent(); } catch {}
      return false;
    }

    await this.driver.switchTo().defaultContent();
    await this.driver.sleep(1500);

    // Wait for challenge to close
    const closed = await this.waitForChallengeToClose(15000);
    return closed;
  }

  async waitForChallengeToClose(timeoutMs = 15000) {
    const start = Date.now();
    while (Date.now() - start < timeoutMs) {
      try {
        await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
        // still present
        await this.driver.sleep(800);
      } catch {
              return true;
            }
          }
          return false;
  }

  async isChallengeOpen() {
    try {
      // Check for challenge iframe
      await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
      
      // Also check if the challenge is actually visible/interactive
      const iframes = await this.driver.findElements(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
      if (iframes.length > 0) {
        try {
          const isDisplayed = await iframes[0].isDisplayed();
          return isDisplayed;
        } catch {
          return true; // If we can't check display state, assume it's open
        }
      }
      return true;
    } catch {
      return false;
    }
  }

  async checkForCaptchaToken() {
    try {
      // Check for hCaptcha response token in various forms
      const tokenChecks = [
        // Common hCaptcha response field names
        'textarea[name="h-captcha-response"]',
        'input[name="h-captcha-response"]',
        'textarea[name="g-recaptcha-response"]',
        'input[name="g-recaptcha-response"]'
      ];
      
      for (const selector of tokenChecks) {
        try {
          const element = await this.driver.findElement(By.css(selector));
          const value = await element.getAttribute('value');
          if (value && value.length > 10) {
            console.log('✅ Found captcha response token');
            return true;
          }
        } catch {
          // Continue to next selector
        }
      }
      
      // Check for hCaptcha widget state
      try {
        const widget = await this.driver.findElement(By.css('.h-captcha'));
        const classes = await widget.getAttribute('class');
        if (classes && classes.includes('h-captcha-success')) {
          console.log('✅ Found successful captcha widget state');
          return true;
        }
      } catch {
        // Widget not found or no success class
      }
      
      return false;
    } catch (e) {
      console.log('Error checking for captcha token:', e.message);
      return false;
    }
  }



  async solveCaptcha() {
    console.log('Attempting to solve hCaptcha using SolveCaptcha API with coordinates method...');

    try {
      const apiKeyValid = await this.testApiKey();
      if (!apiKeyValid) throw new Error('Invalid API key');

      const sitekey = await this.extractSitekey();
      const pageUrl = await this.driver.getCurrentUrl();

      console.log('Using sitekey:', sitekey);
      console.log('Page URL:', pageUrl);

      // Multi-round solving loop to handle multi-page challenges
      for (let round = 1; round <= this.maxRoundsPerChallenge; round++) {
        console.log(`🔄 Starting round ${round}/${this.maxRoundsPerChallenge}`);

        // Check if challenge is still present before attempting to solve
        if (!(await this.isChallengeOpen())) {
          console.log('✅ Challenge is no longer open - captcha appears to be solved!');
          return true;
        }

        const taskId = await this.submitCaptcha(sitekey, pageUrl);
        if (!taskId) {
          console.log('❌ Failed to submit captcha for solving');
          return false;
        }

        console.log('⏳ Waiting for solution...');
        await this.driver.sleep(15000);

        const solution = await this.pollForSolution(taskId);
        if (!solution) {
          console.log('❌ Failed to get solution from API');
          return false;
        }

        // Log solution shape for debugging
        try {
          console.log('Solution payload type:', typeof solution.coordinates);
        } catch {}

        console.log('🔧 Executing coordinates-based captcha solving...');
        await this.executeCoordinates(solution.coordinates);

        // Wait a bit for the challenge to process
        await this.driver.sleep(2000);

        // Check multiple ways if captcha is solved
        const challengeStillOpen = await this.isChallengeOpen();
        const tokenPresent = await this.checkForCaptchaToken();

        if (!challengeStillOpen || tokenPresent) {
          console.log('✅ Captcha appears to be solved - challenge closed or token found!');
          await this.waitForChallengeToClose(5000);
          return true;
        }

        // If still open, small delay and try another round
        console.log(`Challenge still open after round ${round}, continuing...`);
        await this.driver.sleep(2000);
      }

      console.log('❌ Exceeded maximum rounds, captcha solving failed');
      return false;
    } catch (e) {
      console.log('Error solving captcha with API:', e.message);
      return false;
    }
  }

  async handleCaptcha() {
    console.log('Checking for hCaptcha...');
    
    // First check if captcha is already solved
    if (await this.checkForCaptchaToken()) {
      console.log('✅ Captcha token already present - captcha appears to be solved');
      return true;
    }
    
    if (!(await this.detectHCaptcha())) {
      console.log('No hCaptcha detected');
      return true;
    }

    console.log('hCaptcha detected, attempting to solve with coordinates method...');
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      console.log(`\n🔄 Attempt ${attempt}/${this.maxRetries}`);
      
      // Check if captcha was solved before starting this attempt
      if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
        console.log('✅ Captcha appears to be solved before attempt - skipping');
        return true;
      }
      
      try {
        const solved = await this.solveCaptcha();
        if (solved) {
          console.log('🎉 hCaptcha solved successfully with coordinates method!');
          return true;
        }
        
        console.log(`❌ Attempt ${attempt} failed`);
        
        // Don't refresh the page as it might interfere with the login process
        // Instead, just wait and check if captcha state changed
        if (attempt < this.maxRetries) {
          console.log('⏳ Waiting before next attempt...');
          await this.driver.sleep(3000);
          
          // Check again if captcha was solved during the wait
          if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
            console.log('✅ Captcha appears to be solved during wait period');
            return true;
          }
        }
      } catch (e) {
        console.log('❌ Error during captcha solving:', e.message);
        
        // If there's an error, check if the captcha might have been solved anyway
        if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
          console.log('✅ Error occurred but captcha appears to be solved');
          return true;
        }
      }
    }

    console.log('💔 Failed to solve hCaptcha after all attempts');
    
    // Final check - sometimes the captcha gets solved but we miss it
    if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
      console.log('✅ Final check - captcha appears to be solved after all');
      return true;
    }
    
    return false;
  }
}

module.exports = HCaptchaSolver;

/**
 * Usage Example:
 *
 * const { Builder } = require('selenium-webdriver');
 * const HCaptchaSolver = require('./captcha');
 *
 * async function example() {
 *   const driver = await new Builder().forBrowser('chrome').build();
 *   const solver = new HCaptchaSolver(driver, 'your-solvecaptcha-api-key');
 *
 *   try {
 *     await driver.get('https://example.com/page-with-hcaptcha');
 *     const solved = await solver.handleCaptcha();
 *
 *     if (solved) {
 *       console.log('✅ hCaptcha solved successfully!');
 *       // Continue with your automation...
 *     } else {
 *       console.log('❌ Failed to solve hCaptcha');
 *     }
 *   } finally {
 *     await driver.quit();
 *   }
 * }
 */
