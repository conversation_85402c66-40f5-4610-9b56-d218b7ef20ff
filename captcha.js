const { By, until } = require("selenium-webdriver");
const request = require('request');

/**
 * hCaptcha Solver using SolveCaptcha API (coordinates method)
 *
 * Sends full challenge image (base64) + instruction text to API
 * Polls for coordinate clicks and applies them inside the challenge iframe
 */

class HCaptchaSolver {
  constructor(driver, apiKey) {
    if (!driver) {
      throw new Error('Selenium WebDriver instance is required');
    }
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('Valid SolveCaptcha API key is required');
    }

    this.driver = driver;
    this.apiKey = apiKey;
    this.maxRetries = 3;
    this.waitTimeout = 30000;
    this.pollInterval = 2000;
    this.maxPollTime = 180000;
    this.maxRoundsPerChallenge = 2;
    this.challengeContainerSelector = null;

    // SolveCaptcha API endpoints
    this.submitUrl = 'https://api.solvecaptcha.com/in.php';
    this.resultUrl = 'https://api.solvecaptcha.com/res.php';
  }

  /**
   * Get current API balance
   * @returns {Promise<number|null>} Balance amount or null if error
   */
  async getBalance() {
    try {
      const balanceUrl = `${this.resultUrl}?key=${this.apiKey}&action=getbalance&json=1`;
      const response = await this.makeApiRequest(balanceUrl);
      if (response.status === 1) {
        return parseFloat(response.request) || 0;
      }
      return null;
    } catch (e) {
      console.log('Error getting balance:', e.message);
      return null;
    }
  }

  async makeApiRequest(url, formData = null, method = 'GET', customHeaders = {}) {
    return new Promise((resolve, reject) => {
      if (method === 'GET') {
        request({
          method: 'GET',
          url,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...customHeaders
          }
        }, (error, response) => {
          if (error) return reject(error);
          try {
            const jsonResponse = JSON.parse(response.body);
            resolve(jsonResponse);
          } catch (e) {
            resolve({ status: 0, error: 'Invalid JSON response', raw: response.body ? response.body.toString().trim() : 'No response body' });
          }
        });
      } else {
        const options = {
          method: 'POST',
          url,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            ...customHeaders
          }
        };

        if (customHeaders['Content-Type'] === 'application/json') {
          options.json = formData;
        } else {
          options.form = formData; // use x-www-form-urlencoded for captcha providers
        }

        request(options, (error, response) => {
          if (error) return reject(error);
          try {
            const jsonResponse = JSON.parse(response.body);
            resolve(jsonResponse);
          } catch (e) {
            resolve({ status: 0, error: 'Invalid JSON response', raw: response.body ? response.body.toString().trim() : 'No response body' });
          }
        });
      }
    });
  }

  async detectHCaptcha() {
    try {
      await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"]'));
      return true;
    } catch {
      try {
        await this.driver.findElement(By.css('.h-captcha, [data-hcaptcha-response], [data-sitekey]'));
        return true;
      } catch {
        return false;
      }
    }
  }

  async extractSitekey() {
    console.log('Attempting to extract sitekey from page...');
    
    try {
      const sitekeyElements = await this.driver.findElements(By.css('[data-sitekey]'));
      for (const element of sitekeyElements) {
        const sitekey = await element.getAttribute('data-sitekey');
        if (sitekey && sitekey.length > 10) {
          console.log('✓ Found sitekey via data-sitekey:', sitekey);
          return sitekey;
        }
      }
    } catch {}

    try {
      const pageSource = await this.driver.getPageSource();
      const patterns = [
        /data-sitekey=["']([a-f0-9-]{8,})['"]/gi,
        /["']sitekey["']:\s*["']([a-f0-9-]{8,})["']/gi,
        /sitekey:\s*["']([a-f0-9-]{8,})["']/gi,
        /hcaptcha.*?sitekey.*?["']([a-f0-9-]{8,})["']/gi,
        /["']([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})["']/gi
      ];
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(pageSource)) !== null) {
          const sitekey = match[1];
          if (sitekey && sitekey.length >= 8) {
            console.log('✓ Found sitekey in page source with pattern:', pattern.source);
            console.log('✓ Sitekey:', sitekey);
            return sitekey;
          }
        }
      }
    } catch {}

    console.log('❌ Sitekey extraction failed');
    throw new Error('Could not extract sitekey from page');
  }

  async testApiKey() {
    console.log('Testing SolveCaptcha API key...');
    try {
      const balanceUrl = `${this.resultUrl}?key=${this.apiKey}&action=getbalance&json=1`;
      const response = await this.makeApiRequest(balanceUrl);
      if (response.status === 1) {
        console.log('✓ API key is valid. Balance:', response.request);
        return true;
      }
      if (response.raw === 'ERROR_WRONG_USER_KEY') {
        console.log('❌ API key is invalid');
        return false;
      }
      console.log('⚠️ API key test response:', response);
      return true;
    } catch (e) {
      console.log('Error testing API key:', e.message);
      return false;
    }
  }

  async ensureChallengeOpen() {
    // Try to find challenge iframe; if not open, click checkbox to open it
    let challengeIframe;
    try {
      challengeIframe = await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
      return challengeIframe;
    } catch {}

    // Try to click checkbox iframe
    try {
      const checkboxIframe = await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="checkbox"]'));
      await this.driver.switchTo().frame(checkboxIframe);
      try {
        const checkbox = await this.driver.findElement(By.css('div, iframe, body'));
        await checkbox.click();
      } catch {}
      await this.driver.switchTo().defaultContent();
      await this.driver.sleep(1500);
    } catch {}

    // Wait for challenge iframe to appear
    try {
      challengeIframe = await this.driver.wait(
        until.elementLocated(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]')),
        10000
      );
      return challengeIframe;
    } catch (e) {
      console.log('⚠️ Challenge iframe not found:', e.message);
      return null;
    }
  }

  async getChallengeImageAndInstruction() {
    console.log('🔍 Capturing challenge image and instruction...');

    const challengeIframe = await this.ensureChallengeOpen();
    if (!challengeIframe) {
      console.log('❌ Cannot capture challenge: iframe not found');
      return null;
    }

    await this.driver.switchTo().frame(challengeIframe);

    // Try to read instruction text from common selectors
    let instruction = '';
    const instructionSelectors = [
      '.prompt-text',
      '.task-instructions',
      '.challenge-content .prompt-text',
      '.challenge-view .prompt-text',
      'div:contains("Please click")'
    ];
    for (const sel of instructionSelectors) {
      try {
        const el = await this.driver.findElement(By.css(sel));
        const text = await el.getText();
        if (text && text.length > 3) {
          instruction = text;
                break;
              }
      } catch {}
    }
    if (!instruction) {
      try {
        instruction = await this.driver.executeScript('return document.body.innerText || "";');
        const m = instruction.match(/Please\s+click[^\n]+/i);
        instruction = m ? m[0] : 'Click on all images matching the goal';
      } catch {
        instruction = 'Click on all images matching the goal';
      }
    }

    // Find the challenge image/grid container and screenshot it
    const containerSelectors = [
      'div[class*="task-grid"]',
      'div[class*="image-grid"]',
      'div[class*="challenge"]',
      'div[role="dialog"]',
      'body'
    ];

    let containerEl = null;
    for (const sel of containerSelectors) {
      try {
        const el = await this.driver.findElement(By.css(sel));
        if (el) {
          containerEl = el;
              break;
            }
      } catch {}
    }

    if (!containerEl) {
      console.log('⚠️ Could not find a specific container, using body');
      containerEl = await this.driver.findElement(By.css('body'));
      this.challengeContainerSelector = 'body';
    } else {
      // Record selector used for later coordinate mapping
      try {
        // Pick the first matching selector that found this element
        for (const sel of containerSelectors) {
          try {
            const test = await this.driver.findElement(By.css(sel));
            if (test) { this.challengeContainerSelector = sel; break; }
          } catch {}
        }
      } catch {}
    }

    let base64Image = '';
    try {
      base64Image = await containerEl.takeScreenshot(true);
    } catch (e) {
      console.log('❌ Element screenshot failed, fallback to full frame screenshot:', e.message);
      try {
        base64Image = await this.driver.takeScreenshot();
      } catch (e2) {
        console.log('❌ Full screenshot failed:', e2.message);
      }
    }

    // Back to main content
    await this.driver.switchTo().defaultContent();

    if (!base64Image) {
      console.log('❌ No screenshot captured');
      return null;
    }

    console.log('✅ Captured challenge screenshot and instruction');
    return { imageBase64: base64Image, instruction };
  }

  async submitCaptcha(sitekey, pageUrl, useTokenMethod = false) {
    if (useTokenMethod) {
      console.log('Submitting captcha to SolveCaptcha API using hCaptcha token method...');

      const formData = {
        key: this.apiKey,
        method: 'hcaptcha',
        sitekey: sitekey,
        pageurl: pageUrl,
        json: 1
      };

      console.log('📤 Submitting with hCaptcha token method:', {
        key: this.apiKey.substring(0, 8) + '...',
        method: formData.method,
        sitekey: formData.sitekey,
        pageurl: formData.pageurl,
        json: formData.json
      });

      try {
        const response = await this.makeApiRequest(this.submitUrl, formData, 'POST');
        if (response.status === 1) {
          console.log('✓ Captcha submitted successfully. Task ID:', response.request);
          return { taskId: response.request, method: 'token' };
        }
        console.log('❌ Failed to submit captcha:', response);
        return null;
      } catch (e) {
        console.log('Error submitting captcha:', e.message);
        return null;
      }
    } else {
      console.log('Submitting captcha to SolveCaptcha API using coordinates method...');

      // Capture challenge data required by the service
      const challengeData = await this.getChallengeImageAndInstruction();
      if (!challengeData) {
        console.log('❌ Missing challenge data (image/instruction). Cannot submit coordinates captcha.');
        return null;
      }

      const formData = {
        key: this.apiKey,
        method: 'base64',
        sitekey: sitekey,
        pageurl: pageUrl,
        json: 1,
        coordinatescaptcha: 1,
        textinstructions: challengeData.instruction,
        body: challengeData.imageBase64,
        domain: 'hcaptcha.com',
        // Additional parameters to help with jigsaw puzzles
        comment: 'hcaptcha jigsaw puzzle - need drag coordinates',
        lang: 'en'
      };

      console.log('📤 Submitting with coordinates method:', {
        key: this.apiKey.substring(0, 8) + '...',
        method: formData.method,
        sitekey: formData.sitekey,
        pageurl: formData.pageurl,
        json: formData.json,
        coordinatescaptcha: formData.coordinatescaptcha,
        textinstructions: formData.textinstructions.substring(0, 80) + '...'
      });

      try {
        const response = await this.makeApiRequest(this.submitUrl, formData, 'POST');
        if (response.status === 1) {
          console.log('✓ Captcha submitted successfully. Task ID:', response.request);
          return { taskId: response.request, method: 'coordinates', challengeData: challengeData };
        }
        console.log('❌ Failed to submit captcha:', response);
        return null;
      } catch (e) {
        console.log('Error submitting captcha:', e.message);
        return null;
      }
    }
  }

  async pollForSolution(taskId) {
    console.log('Polling for captcha solution...');
    const startTime = Date.now();
    let pollCount = 0;
    
    while (Date.now() - startTime < this.maxPollTime) {
      pollCount++;
      try {
        const url = `${this.resultUrl}?key=${this.apiKey}&action=get&id=${taskId}&json=1`;
        const response = await this.makeApiRequest(url);
        if (response.status === 1) {
          console.log('🎉 Captcha solved successfully! Coordinates received');
          return { coordinates: response.request, userAgent: response.useragent || null };
        }
        if (response.request === 'CAPCHA_NOT_READY' || response.request === 'NOT_READY' || response.request === 'CAPTCHA_NOT_READY') {
          await this.driver.sleep(this.pollInterval);
          continue;
        }
        if (response.request === 'ERROR_CAPTCHA_UNSOLVABLE') {
          console.log('❌ Captcha marked as unsolvable by API');
          return null;
        }
        console.log('❌ Error / unexpected response:', response);
        await this.driver.sleep(this.pollInterval);
      } catch (e) {
        console.log('🚨 Error polling for solution:', e.message);
        await this.driver.sleep(this.pollInterval);
      }
    }

    console.log('⏰ Timeout waiting for captcha solution');
    return null;
  }

  async executeCoordinates(coordinatesPayload, challengeData = null) {
    console.log('🔧 Executing coordinates-based captcha solving...');

    // Ensure browser window is focused before executing mouse actions
    try {
      await this.driver.executeScript(`
        window.focus();
        if (document.hasFocus && !document.hasFocus()) {
          window.focus();
        }
      `);
    } catch (e) {
      console.log('⚠️ Warning: Could not ensure window focus:', e.message);
    }

    // Debug: Log the raw payload
    console.log('📊 Raw coordinates payload:', JSON.stringify(coordinatesPayload, null, 2));

    // Normalize incoming coordinates/actions: accept JSON string, array, or structured object
    let coordsData = null;
    try {
      if (typeof coordinatesPayload === 'string') {
        const str = coordinatesPayload.trim();
        if (str.startsWith('{') || str.startsWith('[')) {
          coordsData = JSON.parse(str);
        }
      } else if (Array.isArray(coordinatesPayload)) {
        coordsData = coordinatesPayload;
      } else if (coordinatesPayload && typeof coordinatesPayload === 'object') {
        coordsData = coordinatesPayload.coordinates || coordinatesPayload.points || coordinatesPayload.answer || coordinatesPayload.clicks || coordinatesPayload;
      }
    } catch (e) {
      console.log('❌ Error parsing coordinates:', e.message);
    }

    if (!coordsData) {
      console.log('❌ Invalid/unknown coordinates payload format');
      console.log('📊 Payload type:', typeof coordinatesPayload);
      console.log('📊 Payload value:', coordinatesPayload);
      return false;
    }

    console.log('✅ Parsed coordinates data:', JSON.stringify(coordsData, null, 2));

    // Ensure challenge is open and switch into iframe
    const challengeIframe = await this.ensureChallengeOpen();
    if (!challengeIframe) return false;
    await this.driver.switchTo().frame(challengeIframe);





    // Use Selenium's native mouse actions for more reliable interaction
    try {
      console.log('🎯 Using Selenium native mouse actions for coordinate execution');

      // Get container element for coordinate reference
      const containerSelectors = [
        'div[class*="task-grid"]',
        'div[class*="image-grid"]',
        'div[class*="challenge"]',
        'div[role="dialog"]',
        'body'
      ];

      let containerEl = null;
      for (const sel of containerSelectors) {
        try {
          containerEl = await this.driver.findElement(By.css(sel));
          if (containerEl) {
            console.log('🎯 Found container element:', sel);
            break;
          }
        } catch {}
      }

      if (!containerEl) {
        containerEl = await this.driver.findElement(By.css('body'));
      }

      // Get container position and size for coordinate mapping
      const containerRect = await this.driver.executeScript(function(el) {
        const rect = el.getBoundingClientRect();
        return {
          x: rect.left,
          y: rect.top,
          width: rect.width,
          height: rect.height
        };
      }, containerEl);

      console.log('🎯 Container rect:', containerRect);

      // Process coordinates and execute actions using Selenium native mouse actions
      let executionLog = [];
      const actions = this.driver.actions();

      // Parse coordinate data for jigsaw puzzle detection
      let clicks = Array.isArray(coordsData) ? coordsData : (coordsData.clicks || coordsData.coordinates || coordsData.points || coordsData.answer || []);
      const drags = coordsData.drags || coordsData.drag || [];

      // Determine captcha type based on instruction text
      const instruction = (challengeData && challengeData.instruction) ? challengeData.instruction.toLowerCase() : '';
      const isDragPuzzle = instruction.includes('drag') || instruction.includes('piece') || instruction.includes('puzzle') || instruction.includes('complete');
      const isClickSelection = instruction.includes('tap') || instruction.includes('click') || instruction.includes('select') || instruction.includes('store') || instruction.includes('find');

      // Special handling for jigsaw puzzles - convert coordinate pairs to drags
      if (Array.isArray(clicks) && clicks.length >= 2 && drags.length === 0 && isDragPuzzle && !isClickSelection) {
        if (clicks.length % 2 === 0) {
          console.log('🧩 Detected jigsaw puzzle - converting coordinates to drag operations');
          const newDrags = [];
          for (let i = 0; i < clicks.length; i += 2) {
            if (i + 1 < clicks.length) {
              newDrags.push({
                from: clicks[i],
                to: clicks[i + 1]
              });
            }
          }
          clicks = [];
          drags.push(...newDrags);
          console.log('🧩 Created', newDrags.length, 'drag operations from coordinates');
        }
      } else if (Array.isArray(clicks) && clicks.length >= 1 && isClickSelection) {
        console.log('🎯 Detected click-selection captcha - keeping coordinates as individual clicks');
      }

      // Handle single coordinate - for jigsaw puzzles, this might be incomplete data
      if (Array.isArray(clicks) && clicks.length === 1 && drags.length === 0) {
        console.log('⚠️ Single coordinate detected for jigsaw puzzle - this may be incomplete data');
        console.log('🎯 Will try clicking at the coordinate, but success is unlikely');
        // For jigsaw puzzles, single coordinates usually don't work, but we'll try anyway
      }

      console.log('🎯 Executing - Clicks:', clicks?.length || 0, 'Drags:', drags?.length || 0);

      // Check if challenge is still open before trying to execute coordinates
      if (!(await this.isChallengeOpen())) {
        console.log('✅ Challenge already closed - captcha appears to be solved!');
        return true;
      }

      // Switch into iframe and use element-origin actions for precise mouse control
      const challengeIframe = await this.ensureChallengeOpen();
      if (!challengeIframe) {
        console.log('⚠️ Challenge iframe not found - captcha may have auto-solved');
        return true;
      }
      await this.driver.switchTo().frame(challengeIframe);

      // Find the actual challenge/puzzle area element within the iframe
      const candidateSelectors = [
        'div[class*="task-grid"]',
        'div[class*="image-grid"]',
        'div[class*="challenge"]',
        'div[class*="puzzle"]',
        'canvas',
        'svg',
        'div[role="dialog"]',
        'body'
      ];
      let challengeEl = null;
      let usedSelector = 'body';
      for (const sel of candidateSelectors) {
        try {
          const el = await this.driver.findElement(By.css(sel));
          if (el) { challengeEl = el; usedSelector = sel; break; }
        } catch {}
      }
      if (!challengeEl) {
        challengeEl = await this.driver.findElement(By.css('body'));
      }

      function toOffsets(p) {
        if (!p) return null;
        let x = null, y = null;
        if (typeof p.x !== 'undefined') x = parseFloat(p.x); else if (Array.isArray(p) && p.length >= 2) x = parseFloat(p[0]);
        if (typeof p.y !== 'undefined') y = parseFloat(p.y); else if (Array.isArray(p) && p.length >= 2) y = parseFloat(p[1]);
        if (isNaN(x) || isNaN(y)) return null;
        return { x: Math.round(x), y: Math.round(y) };
      }

      console.log('🎯 Using element-origin actions on selector:', usedSelector);

      // Execute clicks relative to challenge element
      if (Array.isArray(clicks) && clicks.length > 0) {
        for (let i = 0; i < clicks.length; i++) {
          const off = toOffsets(clicks[i]);
          if (off) {
            console.log('🎯 Executing click', i + 1, 'at offsets', off, 'relative to challenge element');
            await actions
              .move({ origin: challengeEl, x: off.x, y: off.y })
              .pause(100)
              .press()
              .pause(60)
              .release()
              .perform();
            executionLog.push(`Click ${i + 1} at offsets (${off.x}, ${off.y})`);
            await this.driver.sleep(300);
          } else {
            console.log('🎯 Invalid click point:', clicks[i]);
          }
        }
      }

      // Execute drags relative to challenge element
      const dragList = Array.isArray(drags) ? drags : [drags];
      for (let d = 0; d < dragList.length; d++) {
        const it = dragList[d];
        const from = toOffsets(it?.from || it?.start || it?.src || it?.a);
        const to = toOffsets(it?.to || it?.end || it?.dst || it?.b);
        if (from && to) {
          console.log('🎯 Executing drag', d + 1, 'from', from, 'to', to, 'relative to challenge element');
          await actions
            .move({ origin: challengeEl, x: from.x, y: from.y })
            .pause(80)
            .press()
            .pause(120)
            .move({ origin: challengeEl, x: to.x, y: to.y })
            .pause(80)
            .release()
            .perform();
          executionLog.push(`Drag ${d + 1} from (${from.x}, ${from.y}) to (${to.x}, ${to.y})`);
          await this.driver.sleep(500);
        } else {
          console.log('🎯 Invalid drag points:', it);
        }
      }

      // Skipping verify button click per user request; rely on puzzle auto-submit/close
      console.log('⏭️ Skipping verify button click per request; relying on challenge auto-submit.');

      const result = {
        success: true,
        executionLog: executionLog
      };

      console.log('✅ Coordinate execution result:', result);
      console.log('📋 Execution log:', executionLog);
    } catch (e) {
      console.log('❌ Error dispatching coordinate clicks:', e.message);
      try { await this.driver.switchTo().defaultContent(); } catch {}
      return false;
    }

    await this.driver.switchTo().defaultContent();
    await this.driver.sleep(1500);

    // Wait for challenge to close
    const closed = await this.waitForChallengeToClose(15000);
    return closed;
  }

  async waitForChallengeToClose(timeoutMs = 15000) {
    const start = Date.now();
    while (Date.now() - start < timeoutMs) {
      try {
        await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
        // still present
        await this.driver.sleep(800);
      } catch {
              return true;
            }
          }
          return false;
  }

  async isChallengeOpen() {
    try {
      // Check for challenge iframe
      await this.driver.findElement(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
      
      // Also check if the challenge is actually visible/interactive
      const iframes = await this.driver.findElements(By.css('iframe[src*="hcaptcha.com"][src*="frame=challenge"]'));
      if (iframes.length > 0) {
        try {
          const isDisplayed = await iframes[0].isDisplayed();
          return isDisplayed;
        } catch {
          return true; // If we can't check display state, assume it's open
        }
      }
      return true;
    } catch {
      return false;
    }
  }

  async checkForCaptchaToken() {
    try {
      // Check for hCaptcha response token in various forms
      const tokenChecks = [
        // Common hCaptcha response field names
        'textarea[name="h-captcha-response"]',
        'input[name="h-captcha-response"]',
        'textarea[name="g-recaptcha-response"]',
        'input[name="g-recaptcha-response"]'
      ];
      
      for (const selector of tokenChecks) {
        try {
          const element = await this.driver.findElement(By.css(selector));
          const value = await element.getAttribute('value');
          if (value && value.length > 10) {
            console.log('✅ Found captcha response token');
            return true;
          }
        } catch {
          // Continue to next selector
        }
      }
      
      // Check for hCaptcha widget state
      try {
        const widget = await this.driver.findElement(By.css('.h-captcha'));
        const classes = await widget.getAttribute('class');
        if (classes && classes.includes('h-captcha-success')) {
          console.log('✅ Found successful captcha widget state');
          return true;
        }
      } catch {
        // Widget not found or no success class
      }
      
      return false;
    } catch (e) {
      console.log('Error checking for captcha token:', e.message);
      return false;
    }
  }



  async solveCaptcha(useTokenMethod = false) {
    const methodName = useTokenMethod ? 'hCaptcha token method' : 'coordinates method';
    console.log(`Attempting to solve hCaptcha using SolveCaptcha API with ${methodName}...`);

    try {
      const apiKeyValid = await this.testApiKey();
      if (!apiKeyValid) throw new Error('Invalid API key');

      const sitekey = await this.extractSitekey();
      const pageUrl = await this.driver.getCurrentUrl();

      console.log('Using sitekey:', sitekey);
      console.log('Page URL:', pageUrl);

      if (useTokenMethod) {
        // Token method - single submission, no rounds
        const submitResult = await this.submitCaptcha(sitekey, pageUrl, true);
        if (!submitResult) {
          console.log('❌ Failed to submit captcha for solving');
          return false;
        }

        console.log('⏳ Waiting for solution...');
        await this.driver.sleep(15000);

        const solution = await this.pollForSolution(submitResult.taskId);
        if (!solution) {
          console.log('❌ Failed to get solution from API');
          return false;
        }

        // For token method, solution should contain the token
        const token = solution.coordinates || solution.token || solution.answer;
        if (!token) {
          console.log('❌ No token received from API');
          return false;
        }

        console.log('🔧 Setting hCaptcha token...');
        const tokenSet = await this.setHCaptchaToken(token);

        if (tokenSet) {
          console.log('✅ Token set successfully!');
          await this.driver.sleep(2000);
          return true;
        } else {
          console.log('❌ Failed to set token');
          return false;
        }
      } else {
        // Coordinates method - multi-round solving loop
        for (let round = 1; round <= this.maxRoundsPerChallenge; round++) {
          console.log(`🔄 Starting round ${round}/${this.maxRoundsPerChallenge}`);

          // Check if challenge is still present before attempting to solve
          if (!(await this.isChallengeOpen())) {
            console.log('✅ Challenge is no longer open - captcha appears to be solved!');
            return true;
          }

          const submitResult = await this.submitCaptcha(sitekey, pageUrl, false);
          if (!submitResult) {
            console.log('❌ Failed to submit captcha for solving');
            return false;
          }

          console.log('⏳ Waiting for solution...');
          await this.driver.sleep(15000);

          const solution = await this.pollForSolution(submitResult.taskId);
          if (!solution) {
            console.log('❌ Failed to get solution from API');
            return false;
          }

          // Log solution shape for debugging
          try {
            console.log('Solution payload type:', typeof solution.coordinates);
          } catch {}

          console.log('🔧 Executing coordinates-based captcha solving...');
          await this.executeCoordinates(solution.coordinates, submitResult.challengeData);

          // Wait a bit for the challenge to process
          await this.driver.sleep(2000);

          // Check multiple ways if captcha is solved
          const challengeStillOpen = await this.isChallengeOpen();
          const tokenPresent = await this.checkForCaptchaToken();

          if (!challengeStillOpen || tokenPresent) {
            console.log('✅ Captcha appears to be solved - challenge closed or token found!');
            await this.waitForChallengeToClose(5000);
            return true;
          }

          // If still open, small delay and try another round
          console.log(`Challenge still open after round ${round}, continuing...`);
          await this.driver.sleep(2000);
        }

        console.log('❌ Exceeded maximum rounds, captcha solving failed');
        return false;
      }
    } catch (e) {
      console.log('Error solving captcha with API:', e.message);
      return false;
    }
  }

  async handleCaptcha() {
    console.log('Checking for hCaptcha...');
    
    // First check if captcha is already solved
    if (await this.checkForCaptchaToken()) {
      console.log('✅ Captcha token already present - captcha appears to be solved');
      return true;
    }
    
    if (!(await this.detectHCaptcha())) {
      console.log('No hCaptcha detected');
      return true;
    }

    console.log('hCaptcha detected, attempting to solve with coordinates method...');
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      console.log(`\n🔄 Attempt ${attempt}/${this.maxRetries}`);
      
      // Check if captcha was solved before starting this attempt
      if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
        console.log('✅ Captcha appears to be solved before attempt - skipping');
        return true;
      }
      
      try {
        // Try coordinates method first (more reliable for this captcha type)
        console.log('🔄 Trying coordinates method...');
        let solved = await this.solveCaptcha(false);
        if (solved) {
          console.log('🎉 hCaptcha solved successfully with coordinates method!');
          return true;
        }

        console.log(`❌ Coordinates method failed on attempt ${attempt}`);

        // If coordinates method failed, try token method as fallback (if supported)
        console.log('🔄 Trying hCaptcha token method as fallback...');
        solved = await this.solveCaptcha(true);
        if (solved) {
          console.log('🎉 hCaptcha solved successfully with token method!');
          return true;
        }

        console.log(`❌ Both methods failed on attempt ${attempt}`);

        // Don't refresh the page as it might interfere with the login process
        // Instead, just wait and check if captcha state changed
        if (attempt < this.maxRetries) {
          console.log('⏳ Waiting before next attempt...');
          await this.driver.sleep(3000);

          // Check again if captcha was solved during the wait
          if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
            console.log('✅ Captcha appears to be solved during wait period');
            return true;
          }
        }
      } catch (e) {
        console.log('❌ Error during captcha solving:', e.message);

        // If there's an error, check if the captcha might have been solved anyway
        if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
          console.log('✅ Error occurred but captcha appears to be solved');
          return true;
        }
      }
    }

    console.log('💔 Failed to solve hCaptcha after all attempts');
    
    // Final check - sometimes the captcha gets solved but we miss it
    if (await this.checkForCaptchaToken() || !(await this.isChallengeOpen())) {
      console.log('✅ Final check - captcha appears to be solved after all');
      return true;
    }
    
    return false;
  }

  async setHCaptchaToken(token) {
    console.log('🔧 Setting hCaptcha token in page...');
    try {
      const setCount = await this.driver.executeScript(function(t) {
        let count = 0;
        // Set hCaptcha response token
        document.querySelectorAll('[name="h-captcha-response"]').forEach((el) => {
          el.value = t;
          el.dispatchEvent(new Event('change', { bubbles: true }));
          el.dispatchEvent(new Event('input', { bubbles: true }));
          count++;
        });

        // Also try g-recaptcha-response as fallback
        document.querySelectorAll('[name="g-recaptcha-response"]').forEach((el) => {
          el.value = t;
          el.dispatchEvent(new Event('change', { bubbles: true }));
          el.dispatchEvent(new Event('input', { bubbles: true }));
          count++;
        });

        // Try to trigger hCaptcha callback if available
        if (window.hcaptcha && window.hcaptcha.execute) {
          try {
            window.hcaptcha.execute();
          } catch(e) {}
        }

        return count;
      }, token);

      if (setCount > 0) {
        console.log(`✅ Token set in ${setCount} field(s)`);
        await this.driver.sleep(2000);
        return true;
      } else {
        console.log('❌ No hCaptcha response fields found');
        return false;
      }
    } catch (e) {
      console.log('❌ Error setting token:', e.message);
      return false;
    }
  }
}

module.exports = HCaptchaSolver;

/**
 * Usage Example:
 *
 * const { Builder } = require('selenium-webdriver');
 * const HCaptchaSolver = require('./captcha');
 *
 * async function example() {
 *   const driver = await new Builder().forBrowser('chrome').build();
 *   const solver = new HCaptchaSolver(driver, 'your-solvecaptcha-api-key');
 *
 *   try {
 *     await driver.get('https://example.com/page-with-hcaptcha');
 *     const solved = await solver.handleCaptcha();
 *
 *     if (solved) {
 *       console.log('✅ hCaptcha solved successfully!');
 *       // Continue with your automation...
 *     } else {
 *       console.log('❌ Failed to solve hCaptcha');
 *     }
 *   } finally {
 *     await driver.quit();
 *   }
 * }
 */
