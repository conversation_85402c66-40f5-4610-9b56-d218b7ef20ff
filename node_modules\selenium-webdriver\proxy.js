// Licensed to the Software Freedom Conservancy (SFC) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The SFC licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

/**
 * @fileoverview Proxy module alias.
 *
 *     var webdriver = require('selenium-webdriver'),
 *         proxy = require('selenium-webdriver/proxy');
 *
 *     var driver = new webdriver.Builder()
 *         .withCapabilities(webdriver.Capabilities.chrome())
 *         .setProxy(proxy.manual({http: 'host:1234'}))
 *         .build();
 */

'use strict'

module.exports = require('./lib/proxy')
