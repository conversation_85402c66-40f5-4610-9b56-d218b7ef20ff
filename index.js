const { Builder, By, until } = require("selenium-webdriver");
const chrome = require("selenium-webdriver/chrome");
const firefox = require("selenium-webdriver/firefox");
const edge = require("selenium-webdriver/edge");
const account = require("./account.json");
const HCaptchaSolver = require("./captcha.js");

// Get browser choice from environment variable or default to firefox
const browserChoice = process.env.BROWSER || 'firefox';

(async () => {
  let driver;
  
  switch (browserChoice.toLowerCase()) {
    case 'chrome':
      console.log("Using Chrome browser...");
      // Configure Chrome options to prevent crashes
      const chromeOptions = new chrome.Options();
      chromeOptions.addArguments(
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--disable-extensions",
        "--disable-plugins",
        "--disable-images",
        "--disable-javascript-harmony-shipping",
        "--disable-background-timer-throttling",
        "--disable-backgrounding-occluded-windows",
        "--disable-renderer-backgrounding",
        "--disable-ipc-flooding-protection",
        "--no-first-run",
        "--no-default-browser-check",
        "--disable-default-apps",
        "--disable-popup-blocking",
        "--disable-prompt-on-repost",
        "--disable-hang-monitor",
        "--disable-sync",
        "--disable-translate",
        "--disable-background-networking",
        "--disable-background-downloads",
        "--disable-client-side-phishing-detection",
        "--disable-component-update",
        "--disable-domain-reliability",
        "--disable-features=TranslateUI"
      );
      chromeOptions.excludeSwitches(['enable-automation']);
      chromeOptions.addArguments('--disable-blink-features=AutomationControlled');
      
      driver = await new Builder()
        .forBrowser("chrome")
        .setChromeOptions(chromeOptions)
        .build();
      break;
      
    case 'firefox':
      console.log("Using Firefox browser...");
      // Configure Firefox options
      const firefoxOptions = new firefox.Options();
      firefoxOptions.addArguments('--disable-blink-features=AutomationControlled');
      firefoxOptions.setPreference('dom.webdriver.enabled', false);
      firefoxOptions.setPreference('useAutomationExtension', false);
      
      driver = await new Builder()
        .forBrowser("firefox")
        .setFirefoxOptions(firefoxOptions)
        .build();
      break;
      
    case 'edge':
      console.log("Using Microsoft Edge browser...");
      // Configure Edge options
      const edgeOptions = new edge.Options();
      edgeOptions.addArguments(
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--disable-web-security",
        "--disable-extensions",
        "--disable-plugins"
      );
      edgeOptions.excludeSwitches(['enable-automation']);
      edgeOptions.addArguments('--disable-blink-features=AutomationControlled');
      
      driver = await new Builder()
        .forBrowser("MicrosoftEdge")
        .setEdgeOptions(edgeOptions)
        .build();
      break;
      
    default:
      console.log(`Unknown browser: ${browserChoice}. Using Firefox as default...`);
      const defaultFirefoxOptions = new firefox.Options();
      defaultFirefoxOptions.addArguments('--disable-blink-features=AutomationControlled');
      defaultFirefoxOptions.setPreference('dom.webdriver.enabled', false);
      defaultFirefoxOptions.setPreference('useAutomationExtension', false);
      
      driver = await new Builder()
        .forBrowser("firefox")
        .setFirefoxOptions(defaultFirefoxOptions)
        .build();
  }

  // Focus the browser window to ensure mouse actions work properly
  console.log("Focusing browser window...");
  try {
    await driver.manage().window().maximize();
    await driver.executeScript("window.focus();");
    // Additional focus methods for better compatibility
    await driver.executeScript(`
      if (document.hasFocus && !document.hasFocus()) {
        window.focus();
      }
      if (window.top !== window.self) {
        window.top.focus();
      }
    `);
    console.log("✅ Browser window focused successfully");
  } catch (e) {
    console.log("⚠️ Warning: Could not focus browser window:", e.message);
  }

  // Initialize captcha solver with API key
  const captchaSolver = new HCaptchaSolver(driver, account.solvecaptcha_api_key);

  await driver.get("https://gamdom.io/");

  // Wait for page to load and try multiple methods to find the button
  try {
    // Method 1: Wait for button with data-testid (most reliable)
    await driver.wait(until.elementLocated(By.css("button[data-testid='signin-nav']")), 10000);
    await driver.findElement(By.css("button[data-testid='signin-nav']")).click();
    console.log("Clicked button using data-testid");
  } catch (error) {
    console.log("Method 1 failed, trying Method 2...");
    
    try {
      // Method 2: Wait for button with text content
      await driver.wait(until.elementLocated(By.xpath("//button[.//p[text()='Sign in']]")), 10000);
      await driver.findElement(By.xpath("//button[.//p[text()='Sign in']]")).click();
      console.log("Clicked button using XPath with text");
    } catch (error) {
      console.log("Method 2 failed, trying Method 3...");
      
      try {
        // Method 3: Try with contains text
        await driver.wait(until.elementLocated(By.xpath("//button[contains(., 'Sign in')]")), 10000);
        await driver.findElement(By.xpath("//button[contains(., 'Sign in')]")).click();
        console.log("Clicked button using contains text");
      } catch (error) {
        console.log("Method 3 failed, trying Method 4...");
        
        try {
          // Method 4: Try with partial text
          await driver.wait(until.elementLocated(By.partialLinkText("Sign in")), 10000);
          await driver.findElement(By.partialLinkText("Sign in")).click();
          console.log("Clicked button using partial link text");
        } catch (error) {
          console.log("All methods failed. Element might not be present or page structure changed.");
          console.log("Error:", error.message);
        }
      }
    }
  }

  // Wait a moment for the login form to appear, then fill in credentials
  console.log("Waiting for login form to appear...");
  await new Promise(resolve => setTimeout(resolve, 3000));

  try {
    // Fill in username field
    console.log("Filling in username...");
    const usernameField = await driver.findElement(By.css("input[name='username']"));
    await usernameField.clear();
    await usernameField.sendKeys(account.username);
    console.log("Username filled: " + account.username);

    // Fill in password field
    console.log("Filling in password...");
    const passwordField = await driver.findElement(By.css("input[name='password']"));
    await passwordField.clear();
    await passwordField.sendKeys(account.password);
    console.log("Password filled: " + account.password);

    // Wait a moment for the Start Playing button to appear
    console.log("Waiting for Start Playing button to appear...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Click the Start Playing button
    console.log("Clicking Start Playing button...");
    const startPlayingButton = await driver.findElement(By.css("button[data-testid='start-playing-login']"));
    await startPlayingButton.click();
    console.log("Clicked Start Playing button successfully!");

    // Wait a moment for captcha to potentially appear
    console.log("Waiting for potential captcha to appear...");
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check for and handle captcha after clicking Start Playing
    console.log("Checking for captcha after clicking Start Playing...");
    
    // Set a timeout for captcha solving to prevent infinite hangs
    const captchaTimeout = 120000; // 2 minutes max for captcha solving
    
    let captchaSolved = false;
    try {
      // Add timeout wrapper around captcha solving
      captchaSolved = await Promise.race([
        captchaSolver.handleCaptcha(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Captcha solving timeout')), captchaTimeout)
        )
      ]);
    } catch (error) {
      if (error.message === 'Captcha solving timeout') {
        console.log("⏰ Captcha solving timed out after 2 minutes");
        captchaSolved = false;
      } else {
        console.log("❌ Error during captcha solving:", error.message);
        captchaSolved = false;
      }
    }
    
    if (captchaSolved) {
      console.log("✅ Captcha solved successfully! Waiting for login to complete...");
      // Wait a bit for the login to process
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check if login was successful by looking for elements that appear after login
      try {
        await driver.wait(until.elementLocated(By.css("button[data-testid='headerWalletButton']")), 10000);
        console.log("🎉 Login successful! Wallet button is now visible.");
      } catch (error) {
        console.log("⚠️ Login may not have completed automatically. Checking page...");
        
        // Try to click Start Playing button again if it's still there
        try {
          const startPlayingButton = await driver.findElement(By.css("button[data-testid='start-playing-login']"));
          console.log("🔄 Start Playing button still visible, clicking again...");
          await startPlayingButton.click();
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          // Check one more time for login success
          try {
            await driver.wait(until.elementLocated(By.css("button[data-testid='headerWalletButton']")), 10000);
            console.log("🎉 Login successful after second attempt!");
          } catch {
            console.log("⚠️ Still couldn't find wallet button - login might need manual completion");
          }
        } catch (buttonError) {
          console.log("Start Playing button not found, login might have worked");
        }
      }
    } else {
      console.log("❌ Captcha solving failed or timed out. Checking if login proceeded anyway...");
      
      // Sometimes login might work even if captcha solving appears to fail
      try {
        await driver.wait(until.elementLocated(By.css("button[data-testid='headerWalletButton']")), 5000);
        console.log("🎉 Surprisingly, login seems to have worked despite captcha issues!");
      } catch {
        console.log("💔 Login appears to be blocked by captcha. Manual intervention may be required.");
        // Wait a bit for potential manual solving
        await new Promise(resolve => setTimeout(resolve, 10000));
      }
    }

    // Wait for the page to load after login
    console.log("Waiting for page to load after login...");
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Click the Wallet button
    console.log("Clicking Wallet button...");
    const walletButton = await driver.findElement(By.css("button[data-testid='headerWalletButton']"));
    await walletButton.click();
    console.log("Clicked Wallet button successfully!");

    // Wait 2 seconds as requested
    console.log("Waiting 2 seconds...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Click the Bitcoin deposit option
    console.log("Clicking Bitcoin deposit option...");
    const bitcoinDeposit = await driver.findElement(By.css("div[data-testid='deposit-crypto-Bitcoin']"));
    await bitcoinDeposit.click();
    console.log("Clicked Bitcoin deposit option successfully!");

    // Wait for the Bitcoin address to appear
    console.log("Waiting for Bitcoin address to appear...");
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Get and log the Bitcoin address
    console.log("Getting Bitcoin address...");
    const bitcoinAddressInput = await driver.findElement(By.css("input[readonly]"));
    const bitcoinAddress = await bitcoinAddressInput.getAttribute("value");
    console.log("Bitcoin Address:", bitcoinAddress);

  } catch (error) {
    console.log("Error in the process:", error.message);
    
    // Alternative methods to find input fields
    try {
      console.log("Trying alternative method to find username field...");
      const usernameField = await driver.findElement(By.xpath("//input[@placeholder='Enter your username']"));
      await usernameField.clear();
      await usernameField.sendKeys(account.username);
      console.log("Username filled using placeholder: " + account.username);
    } catch (altError) {
      console.log("Could not find username field:", altError.message);
    }

    // Alternative method to find Start Playing button
    try {
      console.log("Trying alternative method to find Start Playing button...");
      const startPlayingButton = await driver.findElement(By.xpath("//button[contains(., 'Start Playing')]"));
      await startPlayingButton.click();
      console.log("Clicked Start Playing button using text content");
    } catch (buttonError) {
      console.log("Could not find Start Playing button:", buttonError.message);
    }

    // Alternative method to find Wallet button
    try {
      console.log("Trying alternative method to find Wallet button...");
      const walletButton = await driver.findElement(By.xpath("//button[contains(., 'Wallet')]"));
      await walletButton.click();
      console.log("Clicked Wallet button using text content");
    } catch (walletError) {
      console.log("Could not find Wallet button:", walletError.message);
    }

    // Alternative method to find Bitcoin deposit
    try {
      console.log("Trying alternative method to find Bitcoin deposit...");
      const bitcoinDeposit = await driver.findElement(By.xpath("//div[contains(., 'Bitcoin')]"));
      await bitcoinDeposit.click();
      console.log("Clicked Bitcoin deposit using text content");
    } catch (bitcoinError) {
      console.log("Could not find Bitcoin deposit:", bitcoinError.message);
    }

    // Alternative method to find Bitcoin address
    try {
      console.log("Trying alternative method to find Bitcoin address...");
      const bitcoinAddressInput = await driver.findElement(By.xpath("//input[@readonly]"));
      const bitcoinAddress = await bitcoinAddressInput.getAttribute("value");
      console.log("Bitcoin Address (alternative method):", bitcoinAddress);
    } catch (addressError) {
      console.log("Could not find Bitcoin address:", addressError.message);
    }
  }

  // Keep browser open for 30 seconds so you can see what happens after clicking
  console.log("Browser will stay open for 30 seconds. Press Ctrl+C to close manually.");
  await new Promise(resolve => setTimeout(resolve, 30000));
  
  // Only close if you want to - comment out this line to keep browser open indefinitely
  // await driver.quit();

})();
